import React, { useState, useEffect } from 'react';
import {
  Table,
  Button,
  message,
  Switch,
  Space,
  Tag,
  Empty,
  Modal,
  Spin,
  Drawer,
  Form,
  Input,
  Select,
  Button as AntButton,
  Tooltip,
} from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { FileText, Eye, TrendingUp, FolderOpen, HelpCircle } from 'lucide-react';
import { useFetchClient } from '@strapi/strapi/admin';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';

// Custom styles for Quill editor
const quillStyles = `
  .ql-editor {
    font-family: 'Be Vietnam Pro', sans-serif !important;
    font-size: 14px !important;
    line-height: 1.6 !important;
    min-height: 350px !important;
  }

  .ql-toolbar {
    border-top: none !important;
    border-left: none !important;
    border-right: none !important;
    border-bottom: 1px solid #d9d9d9 !important;
    background-color: #fafafa !important;
  }

  .ql-container {
    border: none !important;
    font-family: 'Be Vietnam Pro', sans-serif !important;
  }

  .ql-editor.ql-blank::before {
    font-style: normal !important;
    color: #bfbfbf !important;
    font-family: 'Be Vietnam Pro', sans-serif !important;
  }
`;

// Inject styles
if (typeof document !== 'undefined') {
  const styleElement = document.createElement('style');
  styleElement.textContent = quillStyles;
  document.head.appendChild(styleElement);
}
import {
  PageContainer,
  Card,
  CardContent,
  FiltersSection,
  SearchBar,
  PageHeader,
  ActionButtonGroup,
  ImageDisplay,
  StyledTable,
  StatsGrid,
  StatsCard,
  SharedImageUpload,
} from './shared';

interface NewsArticle {
  id: number;
  documentId: string;
  title: string;
  content?: string;
  image?: {
    id: number;
    url: string;
    name: string;
  };
  danh_muc?: {
    id: number;
    name: string;
  };
  hot: boolean;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  publishedAt?: string;
}

interface NewsStats {
  totalArticles: number;
  newArticles: number;
  totalViews: number;
  totalCategories: number;
}

const NewsManagement: React.FC = () => {
  const [articles, setArticles] = useState<NewsArticle[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [stats, setStats] = useState<NewsStats>({
    totalArticles: 0,
    newArticles: 0,
    totalViews: 0,
    totalCategories: 0,
  });
  const [deleting, setDeleting] = useState<number | null>(null);

  // Article edit modal states
  const [editArticleVisible, setEditArticleVisible] = useState(false);
  const [editingArticle, setEditingArticle] = useState<NewsArticle | null>(null);
  const [categories, setCategories] = useState<any[]>([]);
  const [uploadedImages, setUploadedImages] = useState<any[]>([]);
  const [isAddMode, setIsAddMode] = useState(false);
  const [submitting, setSubmitting] = useState(false);

  // Form data state
  const [formData, setFormData] = useState({
    title: '',
    content: '',
    danh_muc: '',
    hot: false,
    isActive: true,
  });

  const { get, del, post, put } = useFetchClient();

  // Quill editor configuration
  const quillModules = {
    toolbar: [
      [{ header: [1, 2, 3, 4, 5, 6, false] }],
      ['bold', 'italic', 'underline', 'strike'],
      [{ color: [] }, { background: [] }],
      [{ list: 'ordered' }, { list: 'bullet' }],
      [{ indent: '-1' }, { indent: '+1' }],
      [{ align: [] }],
      ['link', 'image', 'video'],
      ['clean'],
    ],
  };

  const quillFormats = [
    'header',
    'bold',
    'italic',
    'underline',
    'strike',
    'color',
    'background',
    'list',
    'bullet',
    'indent',
    'align',
    'link',
    'image',
    'video',
  ];

  // Fetch articles
  const fetchArticles = async () => {
    setLoading(true);
    try {
      const response = await get('/management/news/articles');
      setArticles(response.data.data || []);
    } catch (error: any) {
      console.error('Error fetching news articles:', error);
      message.error('Có lỗi xảy ra khi tải tin tức');
    } finally {
      setLoading(false);
    }
  };

  // Fetch statistics
  const fetchStats = async () => {
    try {
      // Get articles count
      const articlesResponse = await get('/management/news/articles');
      const articlesData = articlesResponse.data.data || [];

      // Get categories count
      const categoriesResponse = await get('/management/news/categories');
      const categoriesData = categoriesResponse.data.data || [];

      // Calculate new articles (last 7 days)
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
      const newArticles = articlesData.filter(
        (article: NewsArticle) => new Date(article.createdAt) > sevenDaysAgo
      ).length;

      setStats({
        totalArticles: articlesData.length,
        newArticles: newArticles,
        totalViews: articlesData.length * 150, // Mock data for views
        totalCategories: categoriesData.length,
      });
    } catch (error: any) {
      console.error('Error fetching stats:', error);
    }
  };

  const statsData = [
    {
      title: 'Tổng tin tức',
      value: stats.totalArticles.toString(),
      icon: FileText,
      color: 'bg-blue' as const,
    },
    {
      title: 'Tin tức mới',
      value: stats.newArticles.toString(),
      icon: TrendingUp,
      color: 'bg-blue-dark' as const,
    },
    {
      title: 'Tổng số lượt xem',
      value: stats.totalViews.toLocaleString(),
      icon: Eye,
      color: 'bg-green' as const,
    },
    {
      title: 'Tổng danh mục',
      value: stats.totalCategories.toString(),
      icon: FolderOpen,
      color: 'bg-red' as const,
    },
  ];

  useEffect(() => {
    fetchArticles();
    fetchStats();
  }, []);

  // Fetch categories for form
  const fetchCategories = async () => {
    try {
      const response = await get('/management/news/categories');
      setCategories(response.data.data || []);
    } catch (error: any) {
      console.error('Error fetching categories:', error);
    }
  };

  // Reset form helper function
  const resetForm = () => {
    setFormData({
      title: '',
      content: '',
      danh_muc: '',
      hot: false,
      isActive: true,
    });
  };

  // Handle add new article
  const handleAddArticle = async () => {
    setIsAddMode(true);
    setEditingArticle(null);

    // Fetch categories for the form
    await fetchCategories();

    // Reset form and images
    resetForm();
    setUploadedImages([]);
    setEditArticleVisible(true);
  };

  // Handle edit article
  const handleEditArticle = async (article: NewsArticle) => {
    setIsAddMode(false);
    setEditingArticle(article);

    // Fetch categories for the form
    await fetchCategories();

    // Populate form with article data
    setFormData({
      title: article.title || '',
      content: article.content || '',
      danh_muc: article.danh_muc?.id?.toString() || '',
      hot: article.hot || false,
      isActive: article.isActive !== undefined ? article.isActive : true,
    });

    // Set uploaded images if exists
    if (article.image) {
      setUploadedImages([
        {
          uid: article.image.id || Date.now(),
          name: article.image.name || 'image',
          status: 'done',
          url: article.image.url,
        },
      ]);
    } else {
      setUploadedImages([]);
    }

    setEditArticleVisible(true);
  };

  // Handle submit (create/update article)
  const handleEditSubmit = async () => {
    try {
      setSubmitting(true);

      // Validate required fields
      if (!formData.title || !formData.content) {
        message.error('Vui lòng điền đầy đủ thông tin bắt buộc');
        setSubmitting(false);
        return;
      }

      if (uploadedImages.length === 0) {
        message.error('Vui lòng tải lên hình ảnh');
        setSubmitting(false);
        return;
      }

      // Prepare data for API
      const articleData: any = {
        title: formData.title,
        content: formData.content,
        danh_muc: formData.danh_muc || null,
        hot: formData.hot || false,
        isActive: formData.isActive !== undefined ? formData.isActive : true,
      };

      if (isAddMode) {
        // For creating new article, handle image upload with retry mechanism
        let imageId = null;
        const file = uploadedImages[0]; // Only one image
        if (file && file.originFileObj) {
          message.loading('Đang tải lên hình ảnh...', 0);

          try {
            const { uploadFileWithRetry } = await import('../utils/uploadUtils');
            const result = await uploadFileWithRetry(file.originFileObj as File, '/upload', {
              onProgress: (progress) => {
                // Update progress if needed
              },
            });

            message.destroy();

            if (!result.success) {
              message.error(result.error || 'Không thể tải lên hình ảnh');
              setSubmitting(false);
              return;
            }

            if (result.data && result.data.length > 0) {
              imageId = result.data[0].id;
            }
          } catch (uploadError) {
            message.destroy();
            console.error('Error uploading image:', uploadError);
            message.error('Không thể tải lên hình ảnh. Vui lòng thử lại.');
            setSubmitting(false);
            return;
          }
        }

        // Create article with image ID
        const finalData = {
          ...articleData,
          image: imageId,
        };

        await post('/management/news/articles', { data: finalData });
        message.success('Thêm tin tức thành công');
      } else {
        // Update existing article
        let imageId = null;
        const file = uploadedImages[0]; // Only one image

        if (file) {
          if (file.originFileObj) {
            // New image uploaded
            const formData = new FormData();
            formData.append('files', file.originFileObj);

            try {
              const uploadResponse = await post('/upload', formData);
              if (uploadResponse.data && uploadResponse.data.length > 0) {
                imageId = uploadResponse.data[0].id;
              }
            } catch (uploadError) {
              console.error('Error uploading image:', uploadError);
              message.error('Không thể tải lên hình ảnh');
              setSubmitting(false);
              return;
            }
          } else if (file.url) {
            // Keep existing image
            const existingImage = editingArticle?.image;
            if (existingImage && existingImage.url === file.url) {
              imageId = existingImage.id;
            }
          }
        }

        const finalData = { ...articleData };
        if (imageId) {
          finalData.image = imageId;
        }

        await put(`/management/news/articles/${editingArticle?.id}`, { data: finalData });
        message.success('Cập nhật tin tức thành công');
      }

      setEditArticleVisible(false);
      resetForm();
      setUploadedImages([]);
      setIsAddMode(false);
      fetchArticles();
      fetchStats();
    } catch (error) {
      console.error('Error saving article:', error);
      message.error(isAddMode ? 'Không thể thêm tin tức' : 'Không thể cập nhật tin tức');
    } finally {
      setSubmitting(false);
    }
  };

  // Handle delete article
  const handleDelete = async (id: number) => {
    if (deleting === id) return;

    Modal.confirm({
      title: 'Xác nhận xóa',
      content: 'Bạn có chắc chắn muốn xóa bài viết này?',
      okText: 'Xóa',
      cancelText: 'Hủy',
      okType: 'danger',
      onOk: async () => {
        setDeleting(id);
        try {
          await del(`/management/news/articles/${id}`);
          message.success('Xóa bài viết thành công');
          fetchArticles();
          fetchStats();
        } catch (error: any) {
          console.error('Error deleting article:', error);
          message.error(error.response?.data?.message || 'Có lỗi xảy ra khi xóa bài viết');
        } finally {
          setDeleting(null);
        }
      },
    });
  };

  // Filter articles based on search
  const filteredArticles = articles.filter((article) =>
    article.title.toLowerCase().includes(searchText.toLowerCase())
  );

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: 'Hình ảnh',
      dataIndex: 'image',
      key: 'image',
      width: 100,
      render: (image: any) => (
        <ImageDisplay
          src={image?.url}
          alt="Article"
          size={50}
          placeholder="Không có"
          preview={true}
        />
      ),
    },
    {
      title: 'Tiêu đề',
      dataIndex: 'title',
      key: 'title',
      sorter: (a: NewsArticle, b: NewsArticle) => a.title.localeCompare(b.title),
      render: (title: string) => (
        <div style={{ maxWidth: 300 }}>
          <div style={{ fontWeight: 500, color: '#1f2937' }}>{title}</div>
        </div>
      ),
    },
    {
      title: 'Danh mục',
      dataIndex: 'danh_muc',
      key: 'danh_muc',
      width: 150,
      render: (category: any) =>
        category ? (
          <Tag color="blue">{category.name}</Tag>
        ) : (
          <Tag color="default">Chưa phân loại</Tag>
        ),
    },
    {
      title: 'Trạng thái',
      key: 'status',
      width: 120,
      render: (_: any, record: NewsArticle) => (
        <Space direction="vertical" size={4}>
          <Switch
            checked={record.isActive}
            disabled
            size="small"
            checkedChildren="Hoạt động"
            unCheckedChildren="Tạm dừng"
          />
          {record.hot && <Tag color="red">Hot</Tag>}
        </Space>
      ),
    },
    {
      title: 'Ngày tạo',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 150,
      render: (date: string) => new Date(date).toLocaleDateString('vi-VN'),
      sorter: (a: NewsArticle, b: NewsArticle) =>
        new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),
    },
    {
      title: 'Thao tác',
      key: 'actions',
      width: 150,
      render: (_: any, record: NewsArticle) => (
        <ActionButtonGroup
          onEdit={() => handleEditArticle(record)}
          onDelete={() => handleDelete(record.id)}
          deleteConfirmTitle="Xác nhận xóa"
          deleteConfirmDescription="Bạn có chắc chắn muốn xóa bài viết này?"
          showView={false}
          showEdit={true}
          showDelete={true}
          editTooltip="Chỉnh sửa bài viết"
          deleteTooltip="Xóa bài viết"
          deleteLoading={deleting === record.id}
          disabled={deleting !== null}
        />
      ),
    },
  ];

  return (
    <PageContainer>
      <Spin spinning={loading} tip="Đang tải dữ liệu...">
        {/* Stats Cards */}
        <StatsGrid>
          {statsData.map((stat, index) => (
            <StatsCard
              key={index}
              title={stat.title}
              value={stat.value}
              icon={<stat.icon />}
              color={stat.color}
            />
          ))}
        </StatsGrid>

        {/* News Management */}
        <Card>
          <PageHeader
            title="Danh sách tin tức"
            description="Xem và quản lý danh sách tin tức"
            actions={
              <Button
                type="primary"
                icon={<PlusOutlined />}
                size="large"
                onClick={handleAddArticle}
              >
                Thêm tin tức
              </Button>
            }
          />

          <CardContent>
            <FiltersSection>
              <SearchBar
                placeholder="Tìm kiếm tin tức..."
                value={searchText}
                onChange={setSearchText}
              />
            </FiltersSection>

            <StyledTable>
              <Table
                columns={columns}
                dataSource={filteredArticles}
                rowKey="id"
                loading={loading}
                pagination={{
                  total: filteredArticles.length,
                  pageSize: 10,
                  showSizeChanger: true,
                  showQuickJumper: true,
                  showTotal: (total, range) => `${range[0]}-${range[1]} của ${total} tin tức`,
                }}
                scroll={{ x: 800 }}
                locale={{
                  emptyText: (
                    <Empty
                      image={Empty.PRESENTED_IMAGE_SIMPLE}
                      description="Không có tin tức nào"
                    />
                  ),
                }}
              />
            </StyledTable>
          </CardContent>
        </Card>
      </Spin>

      {/* Article Edit Drawer */}
      <Drawer
        title={
          <span
            style={{
              fontWeight: 500,
              fontSize: 14,
              color: 'rgb(52, 64, 84)',
              fontFamily: 'Be Vietnam Pro, sans-serif',
            }}
          >
            {isAddMode ? 'Thêm tin tức' : 'Sửa tin tức'}
          </span>
        }
        placement="right"
        width="60%"
        open={editArticleVisible}
        onClose={() => {
          setEditArticleVisible(false);
          resetForm();
          setUploadedImages([]);
          setIsAddMode(false);
        }}
        footer={
          <div style={{ textAlign: 'right' }}>
            <AntButton
              onClick={() => {
                setEditArticleVisible(false);
                resetForm();
                setUploadedImages([]);
                setIsAddMode(false);
              }}
              style={{ marginRight: 8 }}
            >
              Hủy
            </AntButton>
            <AntButton type="primary" loading={submitting} onClick={handleEditSubmit}>
              {isAddMode ? 'Thêm tin tức' : 'Cập nhật'}
            </AntButton>
          </div>
        }
      >
        <div style={{ fontFamily: 'Be Vietnam Pro, sans-serif' }}>
          {/* Image Upload */}
          <div style={{ marginBottom: 24 }}>
            <label style={{ display: 'flex', alignItems: 'center', gap: 8, marginBottom: 8 }}>
              Hình ảnh
              <Tooltip
                title={
                  <div>
                    <div>Dung lượng tối đa: 2MB</div>
                    <div>Cho phép file định dạng sau: jpg, jpeg, png</div>
                  </div>
                }
                placement="top"
              >
                <span style={{ color: 'rgb(72, 72, 71)', cursor: 'help' }}>
                  <HelpCircle size={14} />
                </span>
              </Tooltip>
            </label>
            <Form.Item>
              <SharedImageUpload
                value={uploadedImages}
                onChange={setUploadedImages}
                maxCount={1}
                accept="image/*"
                uploadText="Tải lên"
              />
            </Form.Item>
          </div>

          {/* Title */}
          <div style={{ marginBottom: 16 }}>
            <label style={{ display: 'block', marginBottom: 8 }}>
              Tiêu đề <span style={{ color: 'red' }}>*</span>
            </label>
            <input
              type="text"
              placeholder="Vui lòng nhập tiêu đề"
              value={formData.title}
              onChange={(e) => setFormData({ ...formData, title: e.target.value })}
              style={{
                width: '100%',
                height: 40,
                borderRadius: 8,
                border: '1px solid #d9d9d9',
                padding: '0 12px',
                fontSize: 14,
                outline: 'none',
              }}
            />
          </div>

          {/* Category */}
          <div style={{ marginBottom: 16 }}>
            <label style={{ display: 'block', marginBottom: 8 }}>
              Danh mục <span style={{ color: 'red' }}>*</span>
            </label>
            <Select
              placeholder="Chọn danh mục"
              value={formData.danh_muc || undefined}
              onChange={(value) => setFormData({ ...formData, danh_muc: value })}
              style={{ width: '100%', height: 40 }}
              allowClear
            >
              {categories.map((category) => (
                <Select.Option key={category.id} value={category.id}>
                  {category.name}
                </Select.Option>
              ))}
            </Select>
          </div>

          {/* Content */}
          <div style={{ marginBottom: 80 }}>
            <label style={{ display: 'block', marginBottom: 8, fontWeight: 500 }}>
              Nội dung <span style={{ color: 'red' }}>*</span>
            </label>
            <div
              style={{
                border: '1px solid #d9d9d9',
                borderRadius: 8,
                overflow: 'hidden',
                backgroundColor: '#fff',
              }}
            >
              <ReactQuill
                theme="snow"
                value={formData.content}
                onChange={(content) => setFormData({ ...formData, content })}
                modules={quillModules}
                formats={quillFormats}
                placeholder="Vui lòng nhập nội dung tin tức..."
                style={{
                  height: '800px',
                  fontFamily: 'Be Vietnam Pro, sans-serif',
                }}
              />
            </div>
          </div>
        </div>
      </Drawer>
    </PageContainer>
  );
};

export default NewsManagement;
