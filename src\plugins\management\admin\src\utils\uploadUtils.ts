import { message } from 'antd';

interface UploadOptions {
  maxRetries?: number;
  retryDelay?: number;
  timeout?: number;
  onProgress?: (progress: number) => void;
}

interface UploadResult {
  success: boolean;
  data?: any;
  error?: string;
}

/**
 * Upload file with retry mechanism and better error handling
 */
export const uploadFileWithRetry = async (
  file: File,
  endpoint: string = '/upload',
  options: UploadOptions = {}
): Promise<UploadResult> => {
  const {
    maxRetries = 3,
    retryDelay = 1000,
    timeout = 30000,
    onProgress
  } = options;

  let lastError: any = null;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      // Validate file before upload
      const validationResult = validateFile(file);
      if (!validationResult.valid) {
        return {
          success: false,
          error: validationResult.error
        };
      }

      // Create FormData
      const formData = new FormData();
      formData.append('files', file);

      // Create upload promise with timeout
      const uploadPromise = fetch(`${strapi.backendURL}${endpoint}`, {
        method: 'POST',
        body: formData,
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('jwtToken') || ''}`,
        },
      });

      // Add timeout wrapper
      const timeoutPromise = new Promise<never>((_, reject) =>
        setTimeout(() => reject(new Error('Upload timeout')), timeout)
      );

      const response = await Promise.race([uploadPromise, timeoutPromise]);

      if (!response.ok) {
        throw new Error(`Upload failed with status: ${response.status}`);
      }

      const result = await response.json();
      
      if (result && result.length > 0) {
        onProgress?.(100);
        return {
          success: true,
          data: result
        };
      } else {
        throw new Error('No data returned from upload');
      }

    } catch (error: any) {
      lastError = error;
      console.error(`Upload attempt ${attempt} failed:`, error);

      // Don't retry for validation errors or client errors
      if (error.message.includes('validation') || 
          error.message.includes('Invalid file') ||
          (error.status && error.status >= 400 && error.status < 500)) {
        break;
      }

      // Wait before retry (except for last attempt)
      if (attempt < maxRetries) {
        await new Promise(resolve => setTimeout(resolve, retryDelay * attempt));
      }
    }
  }

  // All attempts failed
  const errorMessage = getErrorMessage(lastError);
  return {
    success: false,
    error: errorMessage
  };
};

/**
 * Validate file before upload
 */
export const validateFile = (file: File): { valid: boolean; error?: string } => {
  // Check file size (10MB limit)
  const maxSize = 10 * 1024 * 1024; // 10MB
  if (file.size > maxSize) {
    return {
      valid: false,
      error: 'File quá lớn. Kích thước tối đa là 10MB.'
    };
  }

  // Check file type for images
  if (file.type.startsWith('image/')) {
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      return {
        valid: false,
        error: 'Định dạng file không được hỗ trợ. Chỉ chấp nhận: JPG, PNG, GIF, WebP.'
      };
    }
  }

  return { valid: true };
};

/**
 * Upload multiple files with progress tracking
 */
export const uploadMultipleFiles = async (
  files: File[],
  endpoint: string = '/upload',
  options: UploadOptions = {}
): Promise<{ results: UploadResult[]; successCount: number }> => {
  const results: UploadResult[] = [];
  let successCount = 0;

  for (let i = 0; i < files.length; i++) {
    const file = files[i];
    
    // Update progress for multiple files
    const fileProgress = (progress: number) => {
      const overallProgress = ((i * 100) + progress) / files.length;
      options.onProgress?.(overallProgress);
    };

    const result = await uploadFileWithRetry(file, endpoint, {
      ...options,
      onProgress: fileProgress
    });

    results.push(result);
    if (result.success) {
      successCount++;
    }
  }

  return { results, successCount };
};

/**
 * Get user-friendly error message
 */
const getErrorMessage = (error: any): string => {
  if (!error) return 'Lỗi không xác định';

  if (error.message) {
    if (error.message.includes('timeout')) {
      return 'Quá thời gian chờ. Vui lòng thử lại.';
    }
    if (error.message.includes('network') || error.message.includes('fetch')) {
      return 'Lỗi kết nối mạng. Vui lòng kiểm tra kết nối internet.';
    }
    if (error.message.includes('413') || error.message.includes('too large')) {
      return 'File quá lớn. Vui lòng chọn file nhỏ hơn.';
    }
    if (error.message.includes('415') || error.message.includes('unsupported')) {
      return 'Định dạng file không được hỗ trợ.';
    }
  }

  return 'Không thể tải lên file. Vui lòng thử lại.';
};

/**
 * Check network connectivity
 */
export const checkNetworkConnectivity = async (): Promise<boolean> => {
  try {
    const response = await fetch(`${strapi.backendURL}/admin/init`, {
      method: 'HEAD',
      cache: 'no-cache'
    });
    return response.ok;
  } catch {
    return false;
  }
};

/**
 * Show upload progress notification
 */
export const showUploadProgress = (progress: number, fileName: string) => {
  if (progress === 0) {
    message.loading(`Đang tải lên ${fileName}...`, 0);
  } else if (progress === 100) {
    message.destroy();
    message.success(`Tải lên ${fileName} thành công!`);
  }
};

// Global upload configuration
export const UPLOAD_CONFIG = {
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  ALLOWED_IMAGE_TYPES: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'],
  MAX_RETRIES: 3,
  RETRY_DELAY: 1000,
  TIMEOUT: 30000,
} as const;
